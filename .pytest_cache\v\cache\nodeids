["tests/backups/test_extract_single_tone_information_vvi.py::TestExtractSingleToneInformationVvi::test_basic_extraction_perfect_sine", "tests/backups/test_extract_single_tone_information_vvi.py::TestExtractSingleToneInformationVvi::test_different_error_percentages", "tests/backups/test_extract_single_tone_information_vvi.py::TestExtractSingleToneInformationVvi::test_edge_cases", "tests/backups/test_extract_single_tone_information_vvi.py::TestExtractSingleToneInformationVvi::test_frequency_range_search", "tests/backups/test_extract_single_tone_information_vvi.py::TestExtractSingleToneInformationVvi::test_full_range_search", "tests/backups/test_extract_single_tone_information_vvi.py::TestExtractSingleToneInformationVvi::test_invalid_frequency_range", "tests/backups/test_extract_single_tone_information_vvi.py::TestExtractSingleToneInformationVvi::test_multi_channel_waveform", "tests/backups/test_extract_single_tone_information_vvi.py::TestExtractSingleToneInformationVvi::test_noisy_signal", "tests/backups/test_loopback_synchronization.py::test_loopback_synchronization_consistency", "tests/backups/test_loopback_synchronization.py::test_loopback_transfer_function_analysis", "tests/test_cont_sync_sine_aiao.py::TestContSyncSineAIAO::test_context_manager", "tests/test_cont_sync_sine_aiao.py::TestContSyncSineAIAO::test_export_function_calls", "tests/test_cont_sync_sine_aiao.py::TestContSyncSineAIAO::test_hardware_sync_start_stop", "tests/test_cont_sync_sine_aiao.py::TestContSyncSineAIAO::test_initialization", "tests/test_cont_sync_sine_aiao.py::TestContSyncSineAIAO::test_waveform_generation_continuity", "tests/test_estimate_sine_args_accuracy.py::TestEstimateSineArgsAccuracy::test_clean_sine_wave_accuracy", "tests/test_estimate_sine_args_accuracy.py::TestEstimateSineArgsAccuracy::test_different_amplitudes_accuracy", "tests/test_estimate_sine_args_accuracy.py::TestEstimateSineArgsAccuracy::test_different_phases_accuracy", "tests/test_estimate_sine_args_accuracy.py::TestEstimateSineArgsAccuracy::test_multiple_frequencies_accuracy", "tests/test_estimate_sine_args_accuracy.py::TestEstimateSineArgsAccuracy::test_with_noise_robustness", "tests/test_extract_single_tone_information_vvi.py::TestExtractSingleToneInformationVvi::test_basic_extraction_perfect_sine", "tests/test_extract_single_tone_information_vvi.py::TestExtractSingleToneInformationVvi::test_different_error_percentages", "tests/test_extract_single_tone_information_vvi.py::TestExtractSingleToneInformationVvi::test_edge_cases", "tests/test_extract_single_tone_information_vvi.py::TestExtractSingleToneInformationVvi::test_frequency_range_search", "tests/test_extract_single_tone_information_vvi.py::TestExtractSingleToneInformationVvi::test_full_range_search", "tests/test_extract_single_tone_information_vvi.py::TestExtractSingleToneInformationVvi::test_invalid_frequency_range", "tests/test_extract_single_tone_information_vvi.py::TestExtractSingleToneInformationVvi::test_multi_channel_waveform", "tests/test_extract_single_tone_information_vvi.py::TestExtractSingleToneInformationVvi::test_noisy_signal", "tests/test_finite_ai_single_channel.py::test_finite_ai_single_channel_basic", "tests/test_finite_ai_single_channel.py::test_finite_ai_single_channel_different_params", "tests/test_finite_ai_single_channel.py::test_finite_ai_single_channel_multiple_channels", "tests/test_finite_ao_single_channel.py::test_finite_ao_single_channel_basic", "tests/test_finite_ao_single_channel.py::test_finite_ao_single_channel_different_waveforms", "tests/test_finite_ao_single_channel.py::test_finite_ao_single_channel_multi_channel_waveform", "tests/test_finite_ao_single_channel.py::test_finite_ao_single_channel_multiple_channels", "tests/test_finite_ao_single_channel.py::test_finite_ao_single_channel_voltage_range_check", "tests/test_hi_perf_cssio.py::TestHiPerfCSSIO::test_context_manager", "tests/test_hi_perf_cssio.py::TestHiPerfCSSIO::test_data_acquisition_with_export", "tests/test_hi_perf_cssio.py::TestHiPerfCSSIO::test_data_acquisition_without_export", "tests/test_hi_perf_cssio.py::TestHiPerfCSSIO::test_init", "tests/test_hi_perf_cssio.py::TestHiPerfCSSIO::test_repeated_start_stop", "tests/test_hi_perf_cssio.py::TestHiPerfCSSIO::test_start_stop_basic", "tests/test_hiperf_cssio.py::TestHiPerfCSSIOInitialization::test_basic_initialization", "tests/test_hiperf_cssio.py::TestHiPerfCSSIOInitialization::test_initialization_with_waveform_with_sine_args", "tests/test_hiperf_cssio.py::TestHiPerfCSSIOInitialization::test_initialization_with_waveform_without_sine_args", "tests/test_hiperf_cssio.py::TestHiPerfCSSIOMethodOverrides::test_process_data_export_empty_queue", "tests/test_hiperf_cssio.py::TestHiPerfCSSIOMethodOverrides::test_process_data_export_no_export", "tests/test_hiperf_cssio.py::TestHiPerfCSSIOMethodOverrides::test_process_data_export_override", "tests/test_hiperf_cssio.py::TestHiPerfCSSIOMethodOverrides::test_setup_ao_task_override", "tests/test_hiperf_cssio.py::TestHiPerfCSSIOProperties::test_context_manager_support", "tests/test_hiperf_cssio.py::TestHiPerfCSSIOProperties::test_inheritance", "tests/test_motor_controller.py::TestMotorController::test_01_initialization_and_connection", "tests/test_motor_controller.py::TestMotorController::test_02_hardware_info", "tests/test_motor_controller.py::TestMotorController::test_03_motor_parameters", "tests/test_motor_controller.py::TestMotorController::test_04_unit_conversion", "tests/test_motor_controller.py::TestMotorController::test_05_axis_status", "tests/test_motor_controller.py::TestMotorController::test_06_position_query", "tests/test_motor_controller.py::TestMotorController::test_07_small_movement_1d", "tests/test_motor_controller.py::TestMotorController::test_08_absolute_movement_1d", "tests/test_motor_controller.py::TestMotorController::test_09_2d_movement", "tests/test_motor_controller.py::TestMotorController::test_10_zero_distance_movement", "tests/test_motor_controller.py::TestMotorController::test_11_calibration_system", "tests/test_motor_controller.py::TestMotorController::test_12_error_handling", "tests/test_motor_controller.py::TestMotorController::test_13_performance_timing", "tests/test_motor_controller.py::TestMotorController::test_14_resource_management", "tests/test_motor_controller.py::test_motor_controller_creation", "tests/test_package_import.py::test_package_level_import", "tests/test_sine_wave_vvi.py::test_sine_wave_vvi_basic", "tests/test_sine_wave_vvi.py::test_sine_wave_vvi_frequency_accuracy", "tests/test_sine_wave_vvi.py::test_sine_wave_vvi_with_amplitude", "tests/test_sine_wave_vvi.py::test_sine_wave_vvi_with_phase", "tests/test_sine_waveform_vvi.py::TestSineWaveformVvi::test_continuous_generation_phase", "tests/test_sine_waveform_vvi.py::TestSineWaveformVvi::test_continuous_generation_timestamp", "tests/test_sine_waveform_vvi.py::TestSineWaveformVvi::test_different_amplitudes", "tests/test_sine_waveform_vvi.py::TestSineWaveformVvi::test_different_frequencies", "tests/test_sine_waveform_vvi.py::TestSineWaveformVvi::test_init_custom_parameters", "tests/test_sine_waveform_vvi.py::TestSineWaveformVvi::test_init_default_parameters", "tests/test_sine_waveform_vvi.py::TestSineWaveformVvi::test_phase_wrapping", "tests/test_sine_waveform_vvi.py::TestSineWaveformVvi::test_single_generation", "tests/test_sweeper.py::TestSweeper::test_data_save_and_load", "tests/test_sweeper.py::TestSweeper::test_full_sweep_measurement", "tests/test_sweeper.py::TestSweeper::test_motor_calibration", "tests/test_sweeper.py::TestSweeper::test_point_grid_generation", "tests/test_sweeper.py::TestSweeper::test_single_point_measurement", "tests/test_sweeper.py::TestSweeper::test_sweeper_initialization", "tests/test_sweeper.py::TestSweeper::test_sweeper_reset", "tests/test_synchronized_ai_ao_measurement.py::TestSynchronizedAiAoMeasurement::test_basic_measurement", "tests/test_synchronized_ai_ao_measurement.py::TestSynchronizedAiAoMeasurement::test_default_parameters", "tests/test_synchronized_ai_ao_measurement.py::TestSynchronizedAiAoMeasurement::test_different_parameters", "tests/test_synchronized_ai_ao_measurement.py::test_synchronized_ai_ao_measurement_basic", "tests/test_synchronized_ai_ao_measurement.py::test_synchronized_ai_ao_measurement_data_processing", "tests/test_synchronized_ai_ao_measurement.py::test_synchronized_ai_ao_measurement_different_channels", "tests/test_synchronized_ai_ao_measurement.py::test_synchronized_ai_ao_measurement_different_params", "tests/test_synchronized_ai_ao_measurement.py::test_synchronized_ai_ao_measurement_voltage_range_check", "tests/test_waveform_generator.py::TestSineGenerator::test_compatibility_with_original_sine_waveform_vvi", "tests/test_waveform_generator.py::TestSineGenerator::test_default_parameters", "tests/test_waveform_generator.py::TestSineGenerator::test_generate_continuous_waveforms", "tests/test_waveform_generator.py::TestSineGenerator::test_generate_single_waveform", "tests/test_waveform_generator.py::TestSineGenerator::test_initialization", "tests/test_waveform_generator.py::TestSineGenerator::test_initialization_with_timestamp", "tests/test_waveform_generator.py::TestSineGenerator::test_multiple_continuous_generations", "tests/test_waveform_generator.py::TestSineGenerator::test_phase_wrapping", "tests/test_waveform_generator.py::TestSineGenerator::test_polymorphism", "tests/test_waveform_generator.py::TestWaveformGenerator::test_cannot_instantiate_abstract_class", "tests/test_waveform_generator.py::TestWaveformGenerator::test_concrete_subclass_must_implement_generate"]