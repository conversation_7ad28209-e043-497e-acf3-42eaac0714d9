"""
Sweeper类使用演示脚本

该脚本演示了如何使用sweeper400.use.sweeper模块中的Sweeper类
进行声学扫场测量。

使用前请确保：
1. PXIe-1090机箱和PXIe-4468板卡（400Slot2）已连接
2. 步进电机控制器已连接
3. ai0通道已连接传声器
"""

import time
from pathlib import Path

from sweeper400.logger import get_logger
from sweeper400.use.sweeper import (
    Sweeper,
    Point2D,
    get_square_grid,
    get_line_grid,
    load_measurement_data,
)
from sweeper400.move import MotorController
from sweeper400.measure import HiPerfCSSIO
from sweeper400.analyze import (
    init_sampling_info,
    init_sine_args,
    get_sine_cycles,
    Waveform,
    PositiveInt,
)

# 获取日志器
logger = get_logger(__name__)


def create_output_waveform():
    """创建输出波形"""
    logger.info("创建输出波形...")

    # 创建采样信息：1000Hz采样率，1000个采样点（1秒）
    sampling_info = init_sampling_info(1000.0, 1000)

    # 创建100Hz正弦波参数
    sine_args = init_sine_args(frequency=100.0, amplitude=1.0, phase=0.0)

    # 生成完整周期的正弦波
    waveform = get_sine_cycles(sampling_info, sine_args)

    logger.info(
        f"输出波形创建完成：{waveform.duration:.3f}秒，{waveform.samples_num}个采样点"
    )
    return waveform


def create_export_function():
    """创建数据导出函数"""
    exported_data = []

    def export_callback(
        ai_waveform: Waveform, ao_waveform: Waveform, chunks_num: PositiveInt
    ) -> None:
        """数据导出回调函数"""
        logger.info(f"导出第 {chunks_num} 段数据，AI数据长度: {len(ai_waveform)}")
        exported_data.append(
            {
                "chunks_num": chunks_num,
                "ai_length": len(ai_waveform),
                "ao_length": len(ao_waveform),
                "timestamp": ai_waveform.timestamp,
            }
        )

    export_callback.exported_data = exported_data  # type: ignore
    return export_callback


def demo_basic_usage():
    """演示基本使用方法"""
    logger.info("=" * 60)
    logger.info("开始Sweeper基本使用演示")
    logger.info("=" * 60)

    # 1. 创建步进电机控制器
    logger.info("1. 创建步进电机控制器...")
    motor_controller = MotorController()

    if not motor_controller._is_connected:
        logger.error("步进电机控制器未连接，演示终止")
        return

    # 2. 创建输出波形和导出函数
    logger.info("2. 创建输出波形和导出函数...")
    output_waveform = create_output_waveform()
    export_function = create_export_function()

    # 3. 创建HiPerfCSSIO实例
    logger.info("3. 创建HiPerfCSSIO实例...")
    try:
        measure_controller = HiPerfCSSIO(
            ai_channel="400Slot2/ai0",
            ao_channel="400Slot2/ao0",
            output_waveform=output_waveform,
            export_function=export_function,
        )
        measure_controller.start()
        logger.info("HiPerfCSSIO任务启动成功")
    except Exception as e:
        logger.error(f"创建HiPerfCSSIO实例失败: {e}")
        motor_controller.cleanup()
        return

    # 4. 创建测量点阵
    logger.info("4. 创建测量点阵...")
    # 创建一个简单的2x2网格，范围较小
    points = get_square_grid(
        x_start=160.0, x_end=170.0, x_points=2, y_start=150.0, y_end=160.0, y_points=2
    )
    logger.info(f"创建了 {len(points)} 个测量点: {points}")

    # 5. 创建Sweeper实例
    logger.info("5. 创建Sweeper实例...")
    sweeper = Sweeper(
        move_controller=motor_controller,
        measure_controller=measure_controller,
        point_list=points,
        chunks_per_point=3,  # 每个点采集3个chunk
        settle_time=0.5,  # 电机稳定时间0.5秒
    )

    # 6. 演示基本功能
    logger.info("6. 演示基本功能...")

    # 获取当前电机位置
    current_pos = motor_controller.get_current_position_2D()
    logger.info(f"当前电机位置: {current_pos}")

    # 测试移动到第一个点
    first_point = points[0]
    logger.info(f"测试移动到第一个点: {first_point}")
    move_success = sweeper._move_to_point(first_point)
    if move_success:
        logger.info("移动成功")
    else:
        logger.error("移动失败")

    # 测试数据采集系统
    logger.info("测试数据采集系统...")
    export_function.exported_data.clear()  # type: ignore
    measure_controller.enable_export = True
    time.sleep(2.0)  # 等待2秒
    measure_controller.enable_export = False

    exported_data = export_function.exported_data  # type: ignore
    logger.info(f"导出了 {len(exported_data)} 个chunk")

    # 7. 清理资源
    logger.info("7. 清理资源...")
    sweeper.cleanup()
    measure_controller.stop()
    motor_controller.cleanup()

    logger.info("=" * 60)
    logger.info("Sweeper基本使用演示完成")
    logger.info("=" * 60)


def demo_point_grid_generation():
    """演示点阵生成功能"""
    logger.info("=" * 60)
    logger.info("演示点阵生成功能")
    logger.info("=" * 60)

    # 1. 矩形网格
    logger.info("1. 生成矩形网格...")
    square_grid = get_square_grid(0.0, 20.0, 3, 0.0, 20.0, 3)
    logger.info(f"3x3矩形网格，共 {len(square_grid)} 个点:")
    for i, point in enumerate(square_grid):
        logger.info(f"  点 {i+1}: ({point.x:.1f}, {point.y:.1f})")

    # 2. 直线
    logger.info("2. 生成直线...")
    line_grid = get_line_grid(0.0, 0.0, 20.0, 20.0, 5)
    logger.info(f"对角线，共 {len(line_grid)} 个点:")
    for i, point in enumerate(line_grid):
        logger.info(f"  点 {i+1}: ({point.x:.1f}, {point.y:.1f})")

    # 3. 单行扫描
    logger.info("3. 生成单行扫描...")
    single_line = get_square_grid(0.0, 30.0, 6, 10.0, 20.0, 1)
    logger.info(f"单行扫描，共 {len(single_line)} 个点:")
    for i, point in enumerate(single_line):
        logger.info(f"  点 {i+1}: ({point.x:.1f}, {point.y:.1f})")

    logger.info("=" * 60)
    logger.info("点阵生成功能演示完成")
    logger.info("=" * 60)


def demo_data_management():
    """演示数据管理功能"""
    logger.info("=" * 60)
    logger.info("演示数据管理功能")
    logger.info("=" * 60)

    # 创建一些模拟数据
    from sweeper400.use.sweeper import PointRawData
    import numpy as np

    # 模拟测量数据
    mock_data: list[PointRawData] = []
    for i in range(3):
        point = Point2D(float(i * 10), float(i * 10))

        # 创建模拟的AI和AO波形
        sampling_info = init_sampling_info(1000.0, 1000)
        sine_args = init_sine_args(100.0, 1.0, 0.0)

        ai_waveforms = []
        ao_waveforms = []
        for j in range(2):  # 每个点2个chunk
            ai_wave = get_sine_cycles(sampling_info, sine_args)
            ao_wave = get_sine_cycles(sampling_info, sine_args)
            ai_waveforms.append(ai_wave)
            ao_waveforms.append(ao_wave)

        point_data: PointRawData = {
            "position": point,
            "ai_data": ai_waveforms,
            "ao_data": ao_waveforms,
        }
        mock_data.append(point_data)

    logger.info(f"创建了 {len(mock_data)} 个点的模拟数据")

    # 保存数据
    save_path = Path("demo_measurement_data.pkl")
    logger.info(f"保存数据到: {save_path}")

    import pickle

    with open(save_path, "wb") as f:
        pickle.dump(mock_data, f, protocol=pickle.HIGHEST_PROTOCOL)

    # 加载数据
    logger.info("加载数据...")
    loaded_data = load_measurement_data(save_path)

    logger.info(f"加载了 {len(loaded_data)} 个点的数据")
    for i, point_data in enumerate(loaded_data):
        pos = point_data["position"]
        ai_count = len(point_data["ai_data"])
        ao_count = len(point_data["ao_data"])
        logger.info(
            f"  点 {i+1}: 位置({pos.x}, {pos.y}), AI:{ai_count}个chunk, AO:{ao_count}个chunk"
        )

    # 清理临时文件
    if save_path.exists():
        save_path.unlink()
        logger.info("清理临时文件")

    logger.info("=" * 60)
    logger.info("数据管理功能演示完成")
    logger.info("=" * 60)


if __name__ == "__main__":
    logger.info("开始Sweeper演示")

    try:
        # 演示点阵生成功能（不需要硬件）
        demo_point_grid_generation()

        # 演示数据管理功能（不需要硬件）
        demo_data_management()

        # 演示基本使用方法（需要硬件连接）
        demo_basic_usage()

    except KeyboardInterrupt:
        logger.info("用户中断演示")
    except Exception as e:
        logger.error(f"演示过程中发生错误: {e}", exc_info=True)

    logger.info("Sweeper演示结束")
