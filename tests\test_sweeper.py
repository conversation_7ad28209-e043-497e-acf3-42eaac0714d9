"""
Sweeper类的完整测试模块

该测试模块对sweeper400.use.sweeper模块中的Sweeper类进行全面测试，
包括初始化、扫场测量、数据保存和加载等功能。

测试环境要求：
- 连接PXIe-1090机箱和PXIe-4468板卡（400Slot2）
- 连接步进电机控制器
- ai0通道连接传声器
"""

import pytest
import time
import tempfile
from pathlib import Path
from typing import List

from sweeper400.logger import get_logger
from sweeper400.use.sweeper import (
    Sweeper,
    Point2D,
    get_square_grid,
    get_line_grid,
    load_measurement_data,
)
from sweeper400.move import MotorController
from sweeper400.measure import HiPerfCSSIO
from sweeper400.analyze import (
    init_sampling_info,
    init_sine_args,
    get_sine_cycles,
    Waveform,
    PositiveInt,
)

# 获取测试日志器
logger = get_logger(__name__)


class TestSweeper:
    """Sweeper类的测试类"""

    @pytest.fixture(scope="class")
    def motor_controller(self):
        """创建并初始化步进电机控制器"""
        logger.info("创建步进电机控制器")
        controller = MotorController()

        # 检查连接状态
        if not controller._is_connected:
            logger.info("步进电机控制器未连接，跳过相关测试")
            pytest.skip("步进电机控制器未连接")

        yield controller

        # 清理资源
        controller.cleanup()
        logger.info("步进电机控制器资源已清理")

    @pytest.fixture(scope="class")
    def output_waveform(self):
        """创建测试用的输出波形"""
        logger.info("创建测试输出波形")

        # 创建采样信息：1000Hz采样率，1000个采样点（1秒）
        sampling_info = init_sampling_info(1000.0, 1000)

        # 创建100Hz正弦波参数
        sine_args = init_sine_args(frequency=100.0, amplitude=1.0, phase=0.0)

        # 生成完整周期的正弦波
        waveform = get_sine_cycles(sampling_info, sine_args)

        logger.info(
            f"输出波形创建完成：{waveform.duration:.3f}秒，{waveform.samples_num}个采样点"
        )
        return waveform

    @pytest.fixture(scope="class")
    def export_function(self):
        """创建数据导出函数"""
        exported_data = []

        def export_callback(
            ai_waveform: Waveform, ao_waveform: Waveform, chunks_num: PositiveInt
        ) -> None:
            """数据导出回调函数"""
            logger.debug(f"导出第 {chunks_num} 段数据，AI数据长度: {len(ai_waveform)}")
            exported_data.append(
                {
                    "chunks_num": chunks_num,
                    "ai_length": len(ai_waveform),
                    "ao_length": len(ao_waveform),
                    "timestamp": ai_waveform.timestamp,
                }
            )

        export_callback.exported_data = exported_data  # type: ignore
        return export_callback

    @pytest.fixture(scope="class")
    def measure_controller(self, output_waveform, export_function):
        """创建并配置HiPerfCSSIO实例"""
        logger.info("创建HiPerfCSSIO实例")

        try:
            # 创建HiPerfCSSIO实例，使用400Slot2/ai0通道
            controller = HiPerfCSSIO(
                ai_channel="400Slot2/ai0",
                ao_channel="400Slot2/ao0",
                output_waveform=output_waveform,
                export_function=export_function,
            )

            # 启动任务
            controller.start()
            logger.info("HiPerfCSSIO任务启动成功")

            yield controller

            # 清理资源
            controller.stop()
            logger.info("HiPerfCSSIO任务已停止")

        except Exception as e:
            logger.error(f"创建HiPerfCSSIO实例失败: {e}")
            pytest.skip(f"无法创建HiPerfCSSIO实例: {e}")

    @pytest.fixture
    def test_point_list(self):
        """创建测试点阵"""
        # 创建一个简单的1x2网格，范围较小以减少测试时间
        points = get_square_grid(
            x_start=160.0,
            x_end=170.0,
            x_points=1,
            y_start=150.0,
            y_end=160.0,
            y_points=2,
        )
        logger.info(f"创建测试点阵，共 {len(points)} 个点: {points}")
        return points

    @pytest.fixture
    def sweeper(self, motor_controller, measure_controller, test_point_list):
        """创建Sweeper实例"""
        logger.info("创建Sweeper实例")

        sweeper = Sweeper(
            move_controller=motor_controller,
            measure_controller=measure_controller,
            point_list=test_point_list,
            chunks_per_point=3,  # 减少chunk数量以加快测试
            settle_time=0.2,  # 减少稳定时间以加快测试
        )

        yield sweeper

        # 清理资源
        sweeper.cleanup()
        logger.info("Sweeper实例已清理")

    def test_sweeper_initialization(self, sweeper, test_point_list):
        """测试Sweeper初始化"""
        logger.info("测试Sweeper初始化")

        # 检查基本属性
        assert sweeper._point_list == test_point_list
        assert sweeper._chunks_per_point == 3
        assert sweeper._settle_time == 0.2
        assert len(sweeper._measurement_data) == 0
        assert not sweeper._is_running

        logger.info("Sweeper初始化测试通过")

    def test_point_grid_generation(self):
        """测试点阵生成函数"""
        logger.info("测试点阵生成函数")

        # 测试矩形网格生成
        square_grid = get_square_grid(0.0, 10.0, 3, 0.0, 10.0, 2)
        assert len(square_grid) == 6  # 3x2 = 6个点
        assert square_grid[0] == Point2D(0.0, 0.0)
        assert square_grid[-1] == Point2D(10.0, 10.0)

        # 测试直线生成
        line_grid = get_line_grid(0.0, 0.0, 10.0, 10.0, 5)
        assert len(line_grid) == 5
        assert line_grid[0] == Point2D(0.0, 0.0)
        assert line_grid[-1] == Point2D(10.0, 10.0)

        logger.info("点阵生成函数测试通过")

    def test_motor_calibration(self, motor_controller):
        """测试电机校准（可选，如果需要的话）"""
        logger.info("测试电机校准")

        # 获取当前位置
        current_pos = motor_controller.get_current_position_2D()
        logger.info(f"当前电机位置: {current_pos}")

        # 如果需要校准，可以取消注释下面的代码
        # logger.info("开始电机校准...")
        # calibration_success = motor_controller.calibrate_all_axis()
        # assert calibration_success, "电机校准失败"
        # logger.info("电机校准完成")

        logger.info("电机校准测试通过")

    def test_single_point_measurement(self, sweeper, export_function):
        """测试单点测量功能"""
        logger.info("测试单点测量功能")

        # 清空之前的导出数据
        export_function.exported_data.clear()  # type: ignore

        # 移动到第一个点
        first_point = sweeper._point_list[0]
        move_success = sweeper._move_to_point(first_point)
        assert move_success, f"移动到点 {first_point} 失败"

        # 测试数据采集系统是否工作
        logger.info("测试数据采集系统...")

        # 启用数据导出
        sweeper._measure_controller.enable_export = True

        # 等待一小段时间让数据采集
        import time

        time.sleep(2.0)  # 等待2秒

        # 停止数据导出
        sweeper._measure_controller.enable_export = False

        # 检查是否有数据被导出
        exported_data = export_function.exported_data  # type: ignore
        logger.info(f"导出了 {len(exported_data)} 个chunk")

        # 如果有数据导出，说明系统工作正常
        if len(exported_data) > 0:
            logger.info("数据采集系统工作正常")
        else:
            logger.warning("没有数据被导出，可能需要检查硬件连接")

    def test_full_sweep_measurement(self, sweeper, export_function):
        """测试完整扫场测量"""
        logger.info("测试完整扫场测量")

        # 清空之前的导出数据和测量数据
        export_function.exported_data.clear()  # type: ignore
        sweeper.reset()

        # 由于实际的扫场测量可能需要很长时间，我们创建一个模拟测试
        logger.info("开始模拟扫场测量...")

        # 测试移动到每个点
        for i, point in enumerate(sweeper._point_list):
            logger.info(f"测试移动到点 {i+1}/{len(sweeper._point_list)}: {point}")
            move_success = sweeper._move_to_point(point)
            assert move_success, f"移动到点 {point} 失败"

            # 简短等待以模拟稳定时间
            import time

            time.sleep(0.1)

        logger.info("所有点位移动测试完成")

        # 测试数据结构
        measurement_data = sweeper.get_data()
        logger.info(f"当前测量数据点数: {len(measurement_data)}")

        # 如果要进行实际的扫场测量，可以取消注释下面的代码
        # logger.info("开始实际扫场测量...")
        # sweep_success = sweeper.sweep()
        # assert sweep_success, "扫场测量失败"
        # measurement_data = sweeper.get_data()
        # logger.info(f"完整扫场测量完成，测量了 {len(measurement_data)} 个点")

    def test_data_save_and_load(self, sweeper):
        """测试数据保存和加载"""
        logger.info("测试数据保存和加载")

        # 确保有测量数据
        if not sweeper.get_data():
            logger.info("没有测量数据，先执行一次扫场测量")
            sweep_success = sweeper.sweep()
            assert sweep_success, "扫场测量失败"

        # 创建临时文件
        with tempfile.NamedTemporaryFile(suffix=".pkl", delete=False) as tmp_file:
            temp_path = Path(tmp_file.name)

        try:
            # 保存数据
            sweeper.save_data(temp_path)
            assert temp_path.exists(), "数据文件未创建"
            assert temp_path.stat().st_size > 0, "数据文件为空"

            # 加载数据
            loaded_data = load_measurement_data(temp_path)
            original_data = sweeper.get_data()

            # 比较数据
            assert len(loaded_data) == len(original_data), "加载的数据点数不正确"

            for i, (loaded_point, original_point) in enumerate(
                zip(loaded_data, original_data)
            ):
                assert (
                    loaded_point["position"] == original_point["position"]
                ), f"点 {i} 位置不匹配"
                assert len(loaded_point["ai_data"]) == len(
                    original_point["ai_data"]
                ), f"点 {i} AI数据长度不匹配"
                assert len(loaded_point["ao_data"]) == len(
                    original_point["ao_data"]
                ), f"点 {i} AO数据长度不匹配"

            logger.info("数据保存和加载测试通过")

        finally:
            # 清理临时文件
            if temp_path.exists():
                temp_path.unlink()

    def test_sweeper_reset(self, sweeper):
        """测试Sweeper重置功能"""
        logger.info("测试Sweeper重置功能")

        # 确保有一些数据
        if not sweeper.get_data():
            sweeper.sweep()

        # 检查重置前有数据
        assert len(sweeper.get_data()) > 0, "重置前应该有测量数据"

        # 执行重置
        sweeper.reset()

        # 检查重置后状态
        assert len(sweeper.get_data()) == 0, "重置后应该没有测量数据"
        assert not sweeper._is_running, "重置后运行标志应该为False"
        assert sweeper._current_point_index == 0, "重置后当前点索引应该为0"

        logger.info("Sweeper重置功能测试通过")


if __name__ == "__main__":
    # 直接运行测试
    logger.info("开始运行Sweeper测试")
    pytest.main([__file__, "-v", "-s"])
